/**
 * 🎯 TEST UNIFIED APPOINTMENT LIBRARY
 *
 * This file tests the newly built @beauty-crm/platform-appointment-unified library
 * by creating an appointment using the unified schema, domain entity, and repository.
 */

import { PrismaClient } from '@prisma/client';

// Import from the unified library
import {
  // Schema and types
  type Appointment,
  type CreateAppointmentRequest,
  type AppointmentStatus,
  // Domain entity
  AppointmentEntity,
  type IAppointmentProps,
  // Validation
  AppointmentModel,
  // Repository
  SimpleAppointmentRepository,
} from '@beauty-crm/platform-appointment-unified';

/**
 * Test service that uses the unified library
 */
export class UnifiedAppointmentService {
  private repository: SimpleAppointmentRepository;
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
    this.repository = new SimpleAppointmentRepository(this.prisma);
  }

  /**
   * Create an appointment using the unified library
   */
  async createAppointment(
    appointmentData: CreateAppointmentRequest,
  ): Promise<Appointment> {
    console.log('🎯 Creating appointment with unified library...');
    console.log('📋 Input data:', JSON.stringify(appointmentData, null, 2));

    try {
      // Step 1: Validate using AppointmentModel
      console.log('✅ Step 1: Validating with AppointmentModel...');
      const validationModel = new AppointmentModel(appointmentData as any);
      console.log('✅ Validation passed');

      // Step 2: Create domain entity
      console.log('✅ Step 2: Creating domain entity...');
      const entityProps: IAppointmentProps = {
        createdAt: new Date(),
        updatedAt: new Date(),
        salonId: appointmentData.salonId,
        customerId: appointmentData.customerId,
        staffId: appointmentData.staffId,
        treatmentId: appointmentData.treatmentId,
        customerName: appointmentData.customerName,
        customerEmail: appointmentData.customerEmail,
        customerPhone: appointmentData.customerPhone,
        treatmentName: appointmentData.treatmentName,
        treatmentDuration: appointmentData.treatmentDuration,
        treatmentPrice: appointmentData.treatmentPrice,
        startTime: appointmentData.startTime,
        endTime: appointmentData.endTime,
        status: appointmentData.status || 'PENDING',
        notes: appointmentData.notes,
        locale: appointmentData.locale || 'en-US',
        source: appointmentData.source || 'planner',
      };

      const appointmentEntity = new AppointmentEntity(entityProps);
      console.log('✅ Domain entity created');

      // Step 3: Business logic validation
      console.log('✅ Step 3: Running business logic validation...');
      if (!appointmentEntity.canBeScheduled()) {
        throw new Error(
          'Appointment cannot be scheduled - business rules failed',
        );
      }
      console.log('✅ Business rules passed');

      // Step 4: Save to database using repository
      console.log('✅ Step 4: Saving to database...');
      const createdAppointment = await this.repository.create(appointmentData);
      console.log('✅ Appointment saved to database');
      console.log(
        '📋 Created appointment:',
        JSON.stringify(createdAppointment, null, 2),
      );

      return createdAppointment;
    } catch (error) {
      console.error('❌ Error creating appointment:', error);
      throw error;
    }
  }

  /**
   * Get appointment by ID
   */
  async getAppointmentById(id: string): Promise<Appointment | null> {
    console.log(`🔍 Getting appointment by ID: ${id}`);

    try {
      const appointment = await this.repository.findById(id);

      if (appointment) {
        console.log('✅ Appointment found');
        console.log(
          '📋 Appointment data:',
          JSON.stringify(appointment, null, 2),
        );
      } else {
        console.log('❌ Appointment not found');
      }

      return appointment;
    } catch (error) {
      console.error('❌ Error getting appointment:', error);
      throw error;
    }
  }

  /**
   * Get appointments by salon ID
   */
  async getAppointmentsBySalonId(salonId: string): Promise<Appointment[]> {
    console.log(`🏢 Getting appointments for salon: ${salonId}`);

    try {
      const appointments = await this.repository.findBySalonId(salonId);
      console.log(`✅ Found ${appointments.length} appointments`);

      return appointments;
    } catch (error) {
      console.error('❌ Error getting appointments:', error);
      throw error;
    }
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    await this.prisma.$disconnect();
  }
}

/**
 * Test function to demonstrate the unified library
 */
export async function testUnifiedLibrary(): Promise<void> {
  console.log('🚀 Testing Unified Appointment Library');
  console.log('=====================================');

  const service = new UnifiedAppointmentService();

  try {
    // Test data
    const testAppointment: CreateAppointmentRequest = {
      salonId: 'salon_test_123',
      customerId: 'customer_test_123',
      staffId: 'staff_test_123',
      treatmentId: 'treatment_test_123',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+1234567890',
      treatmentName: 'Haircut',
      treatmentDuration: 60,
      treatmentPrice: 50.0,
      startTime: new Date('2024-01-15T10:00:00Z'),
      endTime: new Date('2024-01-15T11:00:00Z'),
      status: 'PENDING' as AppointmentStatus,
      notes: 'Test appointment created with unified library',
      locale: 'en-US',
      source: 'planner' as any,
    };

    // Create appointment
    const createdAppointment = await service.createAppointment(testAppointment);
    console.log('🎉 Appointment created successfully!');

    // Get appointment by ID
    if (createdAppointment.id) {
      await service.getAppointmentById(createdAppointment.id);
    }

    // Get appointments by salon ID
    await service.getAppointmentsBySalonId(testAppointment.salonId);
  } catch (error) {
    console.error('💥 Test failed:', error);
  } finally {
    await service.close();
    console.log('🔚 Test completed');
  }
}

// Run the test if this file is executed directly
if (import.meta.main) {
  testUnifiedLibrary().catch(console.error);
}
