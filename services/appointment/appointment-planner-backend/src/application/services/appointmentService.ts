// Using the new unified library
import {
  type Appointment,
  type CreateAppointmentRequest,
  AppointmentEntity,
  type IAppointmentProps,
  AppointmentModel,
  SimpleAppointmentRepository,
} from '@beauty-crm/platform-appointment-unified';

// Legacy imports (keeping for compatibility)
// import type {
//   Appointment,
//   CreateAppointmentRequest,
// } from '@beauty-crm/platform-appointment-schema';
// import { createAppointmentDomain } from '@beauty-crm/platform-appointment-domain';
import type { AppointmentRepository } from '../../infrastructure/repositories/appointmentRepository';
import type { EmailService } from '../../infrastructure/services/emailService';
import type { OutboxRelayer } from '../../infrastructure/events/OutboxRelayer';
import { PrismaClient } from '@prisma/client';

export class AppointmentService {
  constructor(
    private appointmentRepository: AppointmentRepository,
    private emailService: EmailService,
    private outboxRelayer: OutboxRelayer,
  ) {}

  async createAppointment(
    appointmentData: CreateAppointmentRequest,
  ): Promise<Appointment> {
    const appointmentDomain = createAppointmentDomain(appointmentData);
    const appointment = appointmentDomain.toAppointment();

    const newAppointment = await this.appointmentRepository.create(appointment);

    await this.emailService.sendAppointmentConfirmation(
      newAppointment.customerEmail,
      newAppointment,
    );

    // TODO: Add event to outbox
    // await this.outboxRelayer.add( ... );

    return newAppointment;
  }
}
