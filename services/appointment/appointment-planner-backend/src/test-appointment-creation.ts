/**
 * 🎯 APPOINTMENT CREATION TEST WITH UNIFIED LIBRARY
 *
 * This demonstrates creating an appointment using the unified library
 * with a complete flow: validation → entity → repository
 */

// Import from the unified library
import {
  type Appointment,
  type CreateAppointmentRequest,
  AppointmentEntity,
  type IAppointmentProps,
  AppointmentModel,
  SimpleAppointmentRepository,
} from '../../../../shared-platform-engineering/platform-appointment-unified/dist/index.js';

/**
 * Mock PrismaClient for testing without database
 */
class MockPrismaClient {
  appointment = {
    create: async (data: any) => {
      console.log('🗄️ Mock database: Creating appointment...');
      console.log('📋 Data:', JSON.stringify(data.data, null, 2));

      // Return mock created appointment
      return {
        id: 'mock_appointment_' + Date.now(),
        ...data.data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    },

    findUnique: async ({ where }: any) => {
      console.log('🔍 Mock database: Finding appointment by ID:', where.id);

      if (where.id === 'test_appointment_123') {
        return {
          id: 'test_appointment_123',
          salonId: 'salon_test_123',
          customerId: 'customer_test_123',
          staffId: 'staff_test_123',
          treatmentId: 'treatment_test_123',
          customerName: 'John Doe',
          customerEmail: '<EMAIL>',
          customerPhone: '+1234567890',
          treatmentName: 'Haircut',
          treatmentDuration: 60,
          treatmentPrice: 50.0,
          startTime: new Date('2024-01-15T10:00:00Z'),
          endTime: new Date('2024-01-15T11:00:00Z'),
          status: 'PENDING',
          notes: 'Test appointment',
          locale: 'en-US',
          source: 'planner',
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      }

      return null;
    },

    findMany: async ({ where }: any) => {
      console.log(
        '🔍 Mock database: Finding appointments by salon:',
        where.salonId,
      );

      return [
        {
          id: 'existing_appointment_1',
          salonId: where.salonId,
          customerName: 'Jane Smith',
          treatmentName: 'Manicure',
          startTime: new Date('2024-01-15T09:00:00Z'),
          endTime: new Date('2024-01-15T10:00:00Z'),
          status: 'CONFIRMED',
        },
      ];
    },
  };
}

/**
 * Test appointment creation with the unified library
 */
async function testAppointmentCreation(): Promise<void> {
  console.log('🚀 Testing Appointment Creation with Unified Library');
  console.log('===================================================');

  try {
    // Step 1: Setup mock repository
    console.log('🔧 Step 1: Setting up mock repository...');
    const mockPrisma = new MockPrismaClient() as any;
    const repository = new SimpleAppointmentRepository(mockPrisma);
    console.log('✅ Repository setup complete');

    // Step 2: Prepare appointment data
    console.log('📋 Step 2: Preparing appointment data...');
    const appointmentRequest: CreateAppointmentRequest = {
      salonId: 'salon_test_123',
      customerId: 'customer_test_123',
      staffId: 'staff_test_123',
      treatmentId: 'treatment_test_123',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+1234567890',
      treatmentName: 'Haircut',
      treatmentDuration: 60,
      treatmentPrice: 50.0,
      startTime: new Date('2024-01-15T10:00:00Z'),
      endTime: new Date('2024-01-15T11:00:00Z'),
      status: 'PENDING' as any,
      notes: 'Test appointment created with unified library',
      locale: 'en-US',
      source: 'planner' as any,
    };
    console.log('✅ Appointment data prepared');

    // Step 3: Validate with AppointmentModel
    console.log('✅ Step 3: Validating with AppointmentModel...');
    const validationModel = new AppointmentModel({
      ...appointmentRequest,
      id: 'temp_id',
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    console.log('✅ Validation passed');

    // Step 4: Create domain entity
    console.log('🏗️ Step 4: Creating domain entity...');
    const entityProps: IAppointmentProps = {
      createdAt: new Date(),
      updatedAt: new Date(),
      ...appointmentRequest,
    };
    const appointmentEntity = new AppointmentEntity(entityProps);
    console.log('✅ Domain entity created');
    console.log(
      `📋 Entity details: ${appointmentEntity.customerName} - ${appointmentEntity.treatmentName}`,
    );

    // Step 5: Create appointment via repository
    console.log('💾 Step 5: Creating appointment via repository...');
    const createdAppointment = await repository.create(appointmentRequest);
    console.log('✅ Appointment created successfully!');
    console.log('📋 Created appointment ID:', createdAppointment.id);

    // Step 6: Retrieve appointment by ID
    console.log('🔍 Step 6: Retrieving appointment by ID...');
    const retrievedAppointment = await repository.findById(
      'test_appointment_123',
    );
    if (retrievedAppointment) {
      console.log('✅ Appointment retrieved successfully!');
      console.log(
        `📋 Retrieved: ${retrievedAppointment.customerName} - ${retrievedAppointment.treatmentName}`,
      );
    }

    // Step 7: Get appointments by salon
    console.log('🏢 Step 7: Getting appointments by salon...');
    const salonAppointments = await repository.findBySalonId('salon_test_123');
    console.log(`✅ Found ${salonAppointments.length} appointments for salon`);

    console.log('');
    console.log('🎉 APPOINTMENT CREATION TEST COMPLETED SUCCESSFULLY!');
    console.log(
      '✅ The unified library is working perfectly for appointment creation',
    );
    console.log(
      '✅ All components work together: Schema → Validation → Entity → Repository',
    );
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test if this file is executed directly
if (import.meta.main) {
  testAppointmentCreation().catch(console.error);
}

export { testAppointmentCreation };
