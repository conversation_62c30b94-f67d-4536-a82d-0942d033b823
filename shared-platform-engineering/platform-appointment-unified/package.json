{"name": "@beauty-crm/platform-appointment-unified", "version": "1.0.0", "description": "Unified appointment library consolidating all appointment functionality", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "bun run clean && tsc", "clean": "rm -rf dist", "dev": "tsc --watch", "test": "bun test", "lint": "biome check src", "lint:fix": "biome check --apply src"}, "dependencies": {"class-validator": "^0.14.0", "class-transformer": "^0.5.1", "zod": "^3.22.4", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "bun-types": "latest"}, "peerDependencies": {"prisma": "^5.0.0", "@prisma/client": "^5.0.0"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./schema": {"import": "./dist/schema/index.js", "require": "./dist/schema/index.js", "types": "./dist/schema/index.d.ts"}, "./domain": {"import": "./dist/domain/index.js", "require": "./dist/domain/index.js", "types": "./dist/domain/index.d.ts"}, "./eventing": {"import": "./dist/eventing/index.js", "require": "./dist/eventing/index.js", "types": "./dist/eventing/index.d.ts"}, "./infrastructure": {"import": "./dist/infrastructure/index.js", "require": "./dist/infrastructure/index.js", "types": "./dist/infrastructure/index.d.ts"}}, "files": ["dist", "README.md"], "keywords": ["appointment", "beauty-crm", "domain-driven-design", "typescript"], "author": "Beauty CRM Team", "license": "MIT"}