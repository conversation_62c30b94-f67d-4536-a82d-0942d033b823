/**
 * 🎯 APPOINTMENT REPOSITORY - Extracted Infrastructure Logic
 *
 * This contains the repository implementation extracted from the appointment
 * services, using the schema library for clean, consistent data access.
 */

import type { PrismaClient } from '@prisma/client';
import {
  type Appointment,
  type CreateAppointmentRequest,
  AppointmentStatus,
} from '../schema';

// Note: These imports need to be implemented or removed
// import {
//   prismaToAppointment,
//   appointmentToPrismaCreate,
//   appointmentToPrismaUpdate,
//   buildAppointmentQuery,
//   APPOINTMENT_STATUSES,
// } from '../schema';

// import {
//   AppointmentDomain,
//   AppointmentDomainService,
//   type TimeSlot,
//   type AppointmentFilters,
//   type AppointmentStats,
// } from '../domain';

// ============================================================================
// 🎯 REPOSITORY INTERFACE
// ============================================================================

export interface IAppointmentRepository {
  // Basic CRUD
  findById(id: string): Promise<Appointment | null>;
  findBySalonId(
    salonId: string,
    filters?: AppointmentFilters,
  ): Promise<Appointment[]>;
  findByCustomerId(customerId: string): Promise<Appointment[]>;
  findByDateRange(
    salonId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Appointment[]>;
  findOverlapping(appointment: Appointment): Promise<Appointment[]>;
  create(request: CreateAppointmentRequest): Promise<Appointment>;
  update(appointment: Appointment): Promise<Appointment>;
  delete(id: string): Promise<void>;

  // Business queries
  findAvailableSlots(
    salonId: string,
    date: Date,
    duration: number,
  ): Promise<TimeSlot[]>;
  findPendingAppointments(salonId: string): Promise<Appointment[]>;
  findTodaysAppointments(salonId: string): Promise<Appointment[]>;
  findUpcomingAppointments(
    salonId: string,
    days?: number,
  ): Promise<Appointment[]>;
  getAppointmentStats(
    salonId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<AppointmentStats>;
}

// ============================================================================
// 🎯 REPOSITORY IMPLEMENTATION
// ============================================================================

export class AppointmentRepository implements IAppointmentRepository {
  private domainService: AppointmentDomainService;

  constructor(private prisma: PrismaClient) {
    this.domainService = new AppointmentDomainService();
  }

  // ============================================================================
  // 📋 BASIC CRUD OPERATIONS
  // ============================================================================

  async findById(id: string): Promise<Appointment | null> {
    const prismaAppointment = await this.prisma.appointment.findUnique({
      where: { id },
    });

    // TODO: Implement prismaToAppointment adapter function
    return prismaAppointment as any; // Temporary cast until adapter is implemented
  }

  async findBySalonId(
    salonId: string,
    filters: AppointmentFilters = {},
  ): Promise<Appointment[]> {
    const where = buildAppointmentQuery({
      salonId,
      status: filters.status,
      staffId: filters.staffId,
      startDate: filters.startDate,
      endDate: filters.endDate,
    });

    const prismaAppointments = await this.prisma.appointment.findMany({
      where,
      orderBy: { startTime: 'asc' },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async findByCustomerId(customerId: string): Promise<Appointment[]> {
    const prismaAppointments = await this.prisma.appointment.findMany({
      where: { customerId },
      orderBy: { startTime: 'desc' },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async findByDateRange(
    salonId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Appointment[]> {
    const where = buildAppointmentQuery({
      salonId,
      startDate,
      endDate,
    });

    const prismaAppointments = await this.prisma.appointment.findMany({
      where,
      orderBy: { startTime: 'asc' },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async findOverlapping(appointment: Appointment): Promise<Appointment[]> {
    const prismaAppointments = await this.prisma.appointment.findMany({
      where: {
        salonId: appointment.salonId,
        staffId: appointment.staffId,
        id: { not: appointment.id },
        OR: [
          {
            startTime: {
              lt: appointment.endTime,
            },
            endTime: {
              gt: appointment.startTime,
            },
          },
        ],
        status: {
          notIn: [
            APPOINTMENT_STATUSES.CANCELLED,
            APPOINTMENT_STATUSES.COMPLETED,
          ],
        },
      },
    });

    return prismaAppointments.map(prismaToAppointment);
  }

  async create(request: CreateAppointmentRequest): Promise<Appointment> {
    const prismaData = appointmentToPrismaCreate(request);

    const created = await this.prisma.appointment.create({
      data: prismaData,
    });

    return prismaToAppointment(created);
  }

  async update(appointment: Appointment): Promise<Appointment> {
    const updateData = appointmentToPrismaUpdate(appointment);

    const updated = await this.prisma.appointment.update({
      where: { id: appointment.id },
      data: updateData,
    });

    return prismaToAppointment(updated);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.appointment.delete({
      where: { id },
    });
  }

  // ============================================================================
  // 🎯 BUSINESS QUERIES
  // ============================================================================

  async findAvailableSlots(
    salonId: string,
    date: Date,
    duration: number,
  ): Promise<TimeSlot[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const appointments = await this.findByDateRange(
      salonId,
      startOfDay,
      endOfDay,
    );
    const appointmentDomains = appointments.map((a) =>
      AppointmentDomain.fromAppointment(a),
    );

    return this.domainService.generateAvailableSlots(
      date,
      duration,
      appointmentDomains,
    );
  }

  async findPendingAppointments(salonId: string): Promise<Appointment[]> {
    return this.findBySalonId(salonId, {
      status: APPOINTMENT_STATUSES.PENDING,
    });
  }

  async findTodaysAppointments(salonId: string): Promise<Appointment[]> {
    const today = new Date();
    const startOfDay = new Date(today);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999);

    return this.findByDateRange(salonId, startOfDay, endOfDay);
  }

  async findUpcomingAppointments(
    salonId: string,
    days = 7,
  ): Promise<Appointment[]> {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return this.findByDateRange(salonId, now, futureDate);
  }

  async getAppointmentStats(
    salonId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<AppointmentStats> {
    const appointments = await this.findByDateRange(
      salonId,
      startDate,
      endDate,
    );
    const appointmentDomains = appointments.map((a) =>
      AppointmentDomain.fromAppointment(a),
    );

    return this.domainService.calculateStats(appointmentDomains);
  }

  // ============================================================================
  // 🔄 DOMAIN INTEGRATION HELPERS
  // ============================================================================

  // Domain methods temporarily commented out until AppointmentDomain is implemented
  // async findDomainById(id: string): Promise<AppointmentDomain | null> {
  //   const appointment = await this.findById(id);
  //   return appointment ? AppointmentDomain.fromAppointment(appointment) : null;
  // }

  // async createFromDomain(
  //   domain: AppointmentDomain,
  // ): Promise<AppointmentDomain> {
  //   const appointment = await this.update(domain.toAppointment());
  //   return AppointmentDomain.fromAppointment(appointment);
  // }

  // async updateFromDomain(
  //   domain: AppointmentDomain,
  // ): Promise<AppointmentDomain> {
  //   const appointment = await this.update(domain.toAppointment());
  //   return AppointmentDomain.fromAppointment(appointment);
  // }
}
