/**
 * 📡 APPOINTMENT EVENT PUBLISHER - Unified Event Publishing
 *
 * Handles publishing of all appointment-related events with transactional outbox support.
 */

import type { EventPublisher } from '@beauty-crm/platform-eventing';
import type { PrismaClient } from 'prisma';
import type {
  AppointmentCreatedEvent,
  AppointmentUpdatedEvent,
  AppointmentCancelledEvent,
  AppointmentConfirmedEvent,
  AppointmentCompletedEvent,
} from './appointment-events';
import {
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  createAppointmentCancelledEvent,
  createAppointmentConfirmedEvent,
  createAppointmentCompletedEvent,
} from './appointment-events';
import type { Appointment } from '../schema/appointment-schema';

// ============================================================================
// 🎯 APPOINTMENT EVENT PUBLISHER INTERFACE
// ============================================================================

export interface AppointmentEventPublisher {
  publishAppointmentCreated(
    appointment: Appointment,
    options: {
      source: string;
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void>;

  publishAppointmentUpdated(
    appointment: Appointment,
    changes: Partial<Appointment>,
    options: {
      source: string;
      correlationId?: string;
      userId?: string;
      previousValues?: Partial<Appointment>;
    },
    tx?: any
  ): Promise<void>;

  publishAppointmentCancelled(
    appointmentId: string,
    options: {
      source: string;
      reason?: string;
      cancelledBy?: string;
      refundAmount?: number;
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void>;

  publishAppointmentConfirmed(
    appointmentId: string,
    options: {
      source: string;
      confirmedBy?: string;
      confirmationMethod?: 'email' | 'sms' | 'phone' | 'in_person';
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void>;

  publishAppointmentCompleted(
    appointmentId: string,
    options: {
      source: string;
      completedBy?: string;
      actualDuration?: number;
      customerSatisfaction?: number;
      notes?: string;
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void>;
}

// ============================================================================
// 🔥 IMPLEMENTATION
// ============================================================================

export class AppointmentEventPublisherImpl
  implements AppointmentEventPublisher {
  constructor(
    private eventPublisher: EventPublisher,
    private prisma?: PrismaClient
  ) {}

  async publishAppointmentCreated(
    appointment: Appointment,
    options: {
      source: string;
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void> {
    const event = createAppointmentCreatedEvent(appointment, options);

    if (tx && this.prisma) {
      // Use transactional outbox
      await this.publishWithOutbox(event, tx);
    } else {
      // Direct publish
      await this.eventPublisher.publish(event);
    }
  }

  async publishAppointmentUpdated(
    appointment: Appointment,
    changes: Partial<Appointment>,
    options: {
      source: string;
      correlationId?: string;
      userId?: string;
      previousValues?: Partial<Appointment>;
    },
    tx?: any
  ): Promise<void> {
    const event = createAppointmentUpdatedEvent(appointment, changes, options);

    if (tx && this.prisma) {
      await this.publishWithOutbox(event, tx);
    } else {
      await this.eventPublisher.publish(event);
    }
  }

  async publishAppointmentCancelled(
    appointmentId: string,
    options: {
      source: string;
      reason?: string;
      cancelledBy?: string;
      refundAmount?: number;
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void> {
    const event = createAppointmentCancelledEvent(appointmentId, options);

    if (tx && this.prisma) {
      await this.publishWithOutbox(event, tx);
    } else {
      await this.eventPublisher.publish(event);
    }
  }

  async publishAppointmentConfirmed(
    appointmentId: string,
    options: {
      source: string;
      confirmedBy?: string;
      confirmationMethod?: 'email' | 'sms' | 'phone' | 'in_person';
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void> {
    const event = createAppointmentConfirmedEvent(appointmentId, options);

    if (tx && this.prisma) {
      await this.publishWithOutbox(event, tx);
    } else {
      await this.eventPublisher.publish(event);
    }
  }

  async publishAppointmentCompleted(
    appointmentId: string,
    options: {
      source: string;
      completedBy?: string;
      actualDuration?: number;
      customerSatisfaction?: number;
      notes?: string;
      correlationId?: string;
      userId?: string;
    },
    tx?: any
  ): Promise<void> {
    const event = createAppointmentCompletedEvent(appointmentId, options);

    if (tx && this.prisma) {
      await this.publishWithOutbox(event, tx);
    } else {
      await this.eventPublisher.publish(event);
    }
  }

  private async publishWithOutbox(event: any, tx: any): Promise<void> {
    // Store event in outbox table for reliable delivery
    await tx.outboxEvent.create({
      data: {
        eventId: event.eventId,
        eventType: event.eventType,
        aggregateId: event.aggregateId,
        aggregateType: event.aggregateType,
        eventData: JSON.stringify(event),
        createdAt: new Date(),
        processed: false,
      },
    });
  }
}

// ============================================================================
// 🎯 FACTORY FUNCTION
// ============================================================================

export function createAppointmentEventPublisher(
  eventPublisher: EventPublisher,
  prisma?: PrismaClient
): AppointmentEventPublisher {
  return new AppointmentEventPublisherImpl(eventPublisher, prisma);
}
