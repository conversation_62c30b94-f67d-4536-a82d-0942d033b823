/**
 * 📤 TRANSACTIONAL OUTBOX - Reliable Event Delivery
 *
 * Implements the transactional outbox pattern for guaranteed event delivery.
 */

import type { PrismaClient } from '@prisma/client';
// import type { EventPublisher } from '@beauty-crm/platform-eventing';

// ============================================================================
// 🎯 OUTBOX EVENT INTERFACE
// ============================================================================

export interface OutboxEvent {
  id: string;
  eventId: string;
  eventType: string;
  aggregateId: string;
  aggregateType: string;
  eventData: string; // JSON serialized event
  createdAt: Date;
  processed: boolean;
  processedAt?: Date;
  retryCount: number;
  lastError?: string;
}

// ============================================================================
// 🔥 TRANSACTIONAL OUTBOX IMPLEMENTATION
// ============================================================================

export class TransactionalOutbox {
  constructor(
    private prisma: PrismaClient,
    private eventPublisher: EventPublisher,
    private options: {
      batchSize?: number;
      maxRetries?: number;
      retryDelayMs?: number;
    } = {}
  ) {
    this.options = {
      batchSize: 100,
      maxRetries: 3,
      retryDelayMs: 1000,
      ...options,
    };
  }

  /**
   * Stores an event in the outbox within a transaction
   */
  async storeEvent(event: any, tx: any): Promise<void> {
    await tx.outboxEvent.create({
      data: {
        eventId: event.eventId,
        eventType: event.eventType,
        aggregateId: event.aggregateId,
        aggregateType: event.aggregateType,
        eventData: JSON.stringify(event),
        createdAt: new Date(),
        processed: false,
        retryCount: 0,
      },
    });
  }

  /**
   * Processes unprocessed events from the outbox
   */
  async processOutboxEvents(): Promise<void> {
    const unprocessedEvents = await this.prisma.outboxEvent.findMany({
      where: {
        processed: false,
        retryCount: {
          lt: this.options.maxRetries!,
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
      take: this.options.batchSize,
    });

    for (const outboxEvent of unprocessedEvents) {
      try {
        // Parse and publish the event
        const event = JSON.parse(outboxEvent.eventData);
        await this.eventPublisher.publish(event);

        // Mark as processed
        await this.prisma.outboxEvent.update({
          where: { id: outboxEvent.id },
          data: {
            processed: true,
            processedAt: new Date(),
          },
        });
      } catch (error) {
        // Increment retry count and store error
        await this.prisma.outboxEvent.update({
          where: { id: outboxEvent.id },
          data: {
            retryCount: outboxEvent.retryCount + 1,
            lastError: error instanceof Error ? error.message : 'Unknown error',
          },
        });

        console.error(
          `Failed to process outbox event ${outboxEvent.id}:`,
          error
        );
      }
    }
  }

  /**
   * Starts the outbox processor (runs periodically)
   */
  startProcessor(intervalMs = 5000): NodeJS.Timeout {
    return setInterval(async () => {
      try {
        await this.processOutboxEvents();
      } catch (error) {
        console.error('Error processing outbox events:', error);
      }
    }, intervalMs);
  }

  /**
   * Cleans up old processed events
   */
  async cleanupProcessedEvents(olderThanDays = 7): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    await this.prisma.outboxEvent.deleteMany({
      where: {
        processed: true,
        processedAt: {
          lt: cutoffDate,
        },
      },
    });
  }

  /**
   * Gets outbox statistics
   */
  async getStats(): Promise<{
    total: number;
    processed: number;
    pending: number;
    failed: number;
  }> {
    const [total, processed, pending, failed] = await Promise.all([
      this.prisma.outboxEvent.count(),
      this.prisma.outboxEvent.count({ where: { processed: true } }),
      this.prisma.outboxEvent.count({
        where: {
          processed: false,
          retryCount: { lt: this.options.maxRetries! },
        },
      }),
      this.prisma.outboxEvent.count({
        where: {
          processed: false,
          retryCount: { gte: this.options.maxRetries! },
        },
      }),
    ]);

    return { total, processed, pending, failed };
  }
}
