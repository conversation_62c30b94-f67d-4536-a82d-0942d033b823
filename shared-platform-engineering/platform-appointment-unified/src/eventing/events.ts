// import type { DomainEvent } from '@beauty-crm/platform-eventing';
import type { UnifiedAppointment } from '../schema/appointment-schema';
import { v4 as uuidv4 } from 'uuid';

export const APPOINTMENT_AGGREGATE_TYPE = 'appointment';

// ============================================================================
// Event Payloads
// ============================================================================

export type AppointmentCreatedData = {
  appointment: UnifiedAppointment;
};

export type AppointmentUpdatedData = {
  appointment: UnifiedAppointment;
  changes: Partial<UnifiedAppointment>;
};

export type AppointmentCancelledData = {
  appointmentId: string;
  reason: string;
};

// ============================================================================
// Domain Events
// ============================================================================

export type AppointmentCreatedEvent = DomainEvent<AppointmentCreatedData>;
export type AppointmentUpdatedEvent = DomainEvent<AppointmentUpdatedData>;
export type AppointmentCancelledEvent = DomainEvent<AppointmentCancelledData>;

export type AppointmentEvent =
  | AppointmentCreatedEvent
  | AppointmentUpdatedEvent
  | AppointmentCancelledEvent;

// ============================================================================
// Event Creators
// ============================================================================

type EventCreationOptions = {
  source: string;
  correlationId?: string;
  userId?: string;
};

export function createAppointmentCreatedEvent(
  appointment: UnifiedAppointment,
  options: EventCreationOptions
): AppointmentCreatedEvent {
  return {
    eventId: uuidv4(),
    eventType: 'appointment.created',
    aggregateId: appointment.id,
    aggregateType: APPOINTMENT_AGGREGATE_TYPE,
    data: { appointment },
    timestamp: new Date().toISOString(),
    eventVersion: 1,
    ...options,
  };
}

export function createAppointmentUpdatedEvent(
  appointment: UnifiedAppointment,
  changes: Partial<UnifiedAppointment>,
  options: EventCreationOptions
): AppointmentUpdatedEvent {
  return {
    eventId: uuidv4(),
    eventType: 'appointment.updated',
    aggregateId: appointment.id,
    aggregateType: APPOINTMENT_AGGREGATE_TYPE,
    data: { appointment, changes },
    timestamp: new Date().toISOString(),
    eventVersion: 1,
    ...options,
  };
}

export function createAppointmentCancelledEvent(
  appointmentId: string,
  reason: string,
  options: EventCreationOptions
): AppointmentCancelledEvent {
  return {
    eventId: uuidv4(),
    eventType: 'appointment.cancelled',
    aggregateId: appointmentId,
    aggregateType: APPOINTMENT_AGGREGATE_TYPE,
    data: { appointmentId, reason },
    timestamp: new Date().toISOString(),
    eventVersion: 1,
    ...options,
  };
}
