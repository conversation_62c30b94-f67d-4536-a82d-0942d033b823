/**
 * 🎯 APPOINTMENT EVENTS - Domain Events for Appointment Aggregate
 */

// import type { DomainEvent } from '@beauty-crm/platform-eventing';
import type { Appointment } from '../schema/appointment-schema';

// Simple DomainEvent interface for now
interface DomainEvent<T = any> {
  eventId: string;
  eventType: string;
  aggregateId: string;
  aggregateType: string;
  eventData: T;
  eventVersion: number;
  occurredAt: Date;
  correlationId?: string;
  causationId?: string;
  userId?: string;
}

// ============================================================================
// 🔥 APPOINTMENT EVENT TYPES
// ============================================================================

export interface AppointmentCreatedEvent
  extends DomainEvent<{
    appointment: Appointment;
  }> {
  eventType: 'appointment.created';
  aggregateType: 'appointment';
}

export interface AppointmentUpdatedEvent
  extends DomainEvent<{
    appointment: Appointment;
    changes: Partial<Appointment>;
    previousValues?: Partial<Appointment>;
  }> {
  eventType: 'appointment.updated';
  aggregateType: 'appointment';
}

export interface AppointmentConfirmedEvent
  extends DomainEvent<{
    appointmentId: string;
    confirmedBy?: string;
    confirmationMethod?: 'email' | 'sms' | 'phone' | 'in-person';
  }> {
  eventType: 'appointment.confirmed';
  aggregateType: 'appointment';
}

export interface AppointmentCancelledEvent
  extends DomainEvent<{
    appointmentId: string;
    reason?: string;
    cancelledBy?: string;
    refundAmount?: number;
  }> {
  eventType: 'appointment.cancelled';
  aggregateType: 'appointment';
}

export interface AppointmentCompletedEvent
  extends DomainEvent<{
    appointmentId: string;
    notes?: string;
    completedBy?: string;
    actualDuration?: number;
    customerSatisfaction?: number;
  }> {
  eventType: 'appointment.completed';
  aggregateType: 'appointment';
}

export interface AppointmentRescheduledEvent
  extends DomainEvent<{
    appointmentId: string;
    previousStartTime: Date;
    previousEndTime: Date;
    newStartTime: Date;
    newEndTime: Date;
    rescheduledBy?: string;
    reason?: string;
  }> {
  eventType: 'appointment.rescheduled';
  aggregateType: 'appointment';
}

export interface AppointmentNoShowEvent
  extends DomainEvent<{
    appointmentId: string;
    markedBy?: string;
    penaltyApplied?: boolean;
    penaltyAmount?: number;
  }> {
  eventType: 'appointment.no-show';
  aggregateType: 'appointment';
}

export interface AppointmentStartedEvent
  extends DomainEvent<{
    appointmentId: string;
    startedBy?: string;
    actualStartTime?: Date;
  }> {
  eventType: 'appointment.started';
  aggregateType: 'appointment';
}

// ============================================================================
// 🎯 UNION TYPES
// ============================================================================

export type AppointmentEvent =
  | AppointmentCreatedEvent
  | AppointmentUpdatedEvent
  | AppointmentConfirmedEvent
  | AppointmentCancelledEvent
  | AppointmentCompletedEvent
  | AppointmentRescheduledEvent
  | AppointmentNoShowEvent
  | AppointmentStartedEvent;

// ============================================================================
// 🎯 EVENT BUILDERS
// ============================================================================

export const createAppointmentCreatedEvent = (
  appointment: Appointment,
  metadata?: { correlationId?: string; userId?: string }
): AppointmentCreatedEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.created',
  aggregateId: appointment.id,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointment },
  ...metadata,
});

export const createAppointmentUpdatedEvent = (
  appointment: Appointment,
  changes: Partial<Appointment>,
  previousValues?: Partial<Appointment>,
  metadata?: { correlationId?: string; userId?: string }
): AppointmentUpdatedEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.updated',
  aggregateId: appointment.id,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointment, changes, previousValues },
  ...metadata,
});

export const createAppointmentConfirmedEvent = (
  appointmentId: string,
  data: {
    confirmedBy?: string;
    confirmationMethod?: 'email' | 'sms' | 'phone' | 'in-person';
  },
  metadata?: { correlationId?: string; userId?: string }
): AppointmentConfirmedEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.confirmed',
  aggregateId: appointmentId,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointmentId, ...data },
  ...metadata,
});

export const createAppointmentCancelledEvent = (
  appointmentId: string,
  data: {
    reason?: string;
    cancelledBy?: string;
    refundAmount?: number;
  },
  metadata?: { correlationId?: string; userId?: string }
): AppointmentCancelledEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.cancelled',
  aggregateId: appointmentId,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointmentId, ...data },
  ...metadata,
});

export const createAppointmentCompletedEvent = (
  appointmentId: string,
  data: {
    notes?: string;
    completedBy?: string;
    actualDuration?: number;
    customerSatisfaction?: number;
  },
  metadata?: { correlationId?: string; userId?: string }
): AppointmentCompletedEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.completed',
  aggregateId: appointmentId,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointmentId, ...data },
  ...metadata,
});

export const createAppointmentRescheduledEvent = (
  appointmentId: string,
  data: {
    previousStartTime: Date;
    previousEndTime: Date;
    newStartTime: Date;
    newEndTime: Date;
    rescheduledBy?: string;
    reason?: string;
  },
  metadata?: { correlationId?: string; userId?: string }
): AppointmentRescheduledEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.rescheduled',
  aggregateId: appointmentId,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointmentId, ...data },
  ...metadata,
});

export const createAppointmentNoShowEvent = (
  appointmentId: string,
  data: {
    markedBy?: string;
    penaltyApplied?: boolean;
    penaltyAmount?: number;
  },
  metadata?: { correlationId?: string; userId?: string }
): AppointmentNoShowEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.no-show',
  aggregateId: appointmentId,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointmentId, ...data },
  ...metadata,
});

export const createAppointmentStartedEvent = (
  appointmentId: string,
  data: {
    startedBy?: string;
    actualStartTime?: Date;
  },
  metadata?: { correlationId?: string; userId?: string }
): AppointmentStartedEvent => ({
  eventId: crypto.randomUUID(),
  eventType: 'appointment.started',
  aggregateId: appointmentId,
  aggregateType: 'appointment',
  timestamp: new Date().toISOString(),
  eventVersion: 1,
  source: 'appointment-service',
  data: { appointmentId, ...data },
  ...metadata,
});
