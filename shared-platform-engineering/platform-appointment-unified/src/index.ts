/**
 * 🎯 UNIFIED APPOINTMENT LIBRARY - Main Entry Point
 *
 * This library consolidates all appointment-related functionality:
 * - Schema definitions and validation
 * - Domain entities and business logic
 * - Event definitions and publishers
 * - Repository interfaces and adapters
 * - Application service interfaces
 */

// ============================================================================
// 🔥 SCHEMA LAYER - Single Source of Truth
// ============================================================================
export * from './schema';

// ============================================================================
// 🔥 DOMAIN LAYER - Business Logic
// ============================================================================
export * from './domain';

// ============================================================================
// 🔥 EVENTING LAYER - Domain Events
// ============================================================================
export * from './eventing';

// ============================================================================
// 🔥 INFRASTRUCTURE LAYER - Persistence & External Services
// ============================================================================
export * from './infrastructure';

// ============================================================================
// 🎯 CONVENIENCE RE-EXPORTS
// ============================================================================

// Most commonly used types
export type {
  Appointment,
  AppointmentStatus,
  AppointmentSource,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  AppointmentResponse,
  AppointmentQuery,
  UnifiedAppointment, // Legacy alias
} from './schema/appointment-schema';

export type { IAppointmentProps } from './domain/AppointmentEntity';

export type {
  AppointmentEvent,
  AppointmentCreatedEvent,
  AppointmentUpdatedEvent,
  AppointmentConfirmedEvent,
  AppointmentCancelledEvent,
  AppointmentCompletedEvent,
  AppointmentRescheduledEvent,
  AppointmentNoShowEvent,
  AppointmentStartedEvent,
} from './eventing/appointment-events';

export type {
  IAppointmentRepository,
  IAppointmentFactory,
  IAppointmentDomainService,
  IAppointmentEventPublisher,
  IAppointmentApplicationService,
  AppointmentFilters,
  TimeSlot,
  AppointmentAdapter,
} from './infrastructure/AppointmentRepository';

// Main classes
export { AppointmentEntity } from './domain/AppointmentEntity';

// Validation functions
export {
  validateAppointment,
  validateCreateRequest,
  validateUpdateRequest,
} from './schema/appointment-schema';

// Event builders
export {
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  createAppointmentConfirmedEvent,
  createAppointmentCancelledEvent,
  createAppointmentCompletedEvent,
  createAppointmentRescheduledEvent,
  createAppointmentNoShowEvent,
  createAppointmentStartedEvent,
} from './eventing/appointment-events';

// Schema generators
export { generatePrismaSchema } from './schema/appointment-schema';
