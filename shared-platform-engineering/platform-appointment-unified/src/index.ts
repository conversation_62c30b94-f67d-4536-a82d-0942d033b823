/**
 * 🎯 UNIFIED APPOINTMENT LIBRARY - Main Entry Point
 *
 * This library consolidates all appointment-related functionality:
 * - Schema definitions and validation
 * - Domain entities and business logic
 * - Event definitions and publishers
 * - Repository interfaces and adapters
 * - Application service interfaces
 */

// ============================================================================
// 🔥 SCHEMA LAYER - Single Source of Truth
// ============================================================================
export * from './schema';

// ============================================================================
// 🔥 DOMAIN LAYER - Business Logic
// ============================================================================
export { AppointmentEntity } from './domain/AppointmentEntity';
export type { IAppointmentProps } from './domain/AppointmentEntity';

// ============================================================================
// 🔥 VALIDATION LAYER
// ============================================================================
export { AppointmentModel } from './validation/AppointmentModel';

// ============================================================================
// 🔧 UTILITY EXPORTS
// ============================================================================
export * from './types/unified-types';

// Note: Eventing and Infrastructure exports are temporarily commented out
// until dependencies are resolved
// export * from './eventing';
// export * from './infrastructure';

// ============================================================================
// 🎯 CONVENIENCE RE-EXPORTS
// ============================================================================

// Most commonly used types
export type {
  Appointment,
  AppointmentStatus,
  AppointmentSource,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  AppointmentResponse,
  AppointmentQuery,
  UnifiedAppointment, // Legacy alias
} from './schema/appointment-schema';

// Note: Event and infrastructure type exports are temporarily commented out
// until dependencies are resolved
// export type {
//   AppointmentEvent,
//   AppointmentCreatedEvent,
//   AppointmentUpdatedEvent,
//   AppointmentConfirmedEvent,
//   AppointmentCancelledEvent,
//   AppointmentCompletedEvent,
//   AppointmentRescheduledEvent,
//   AppointmentNoShowEvent,
//   AppointmentStartedEvent,
// } from './eventing/appointment-events';

// export type {
//   IAppointmentRepository,
//   IAppointmentFactory,
//   IAppointmentDomainService,
//   IAppointmentEventPublisher,
//   IAppointmentApplicationService,
//   AppointmentFilters,
//   TimeSlot,
//   AppointmentAdapter,
// } from './infrastructure/AppointmentRepository';

// Validation functions
export {
  validateAppointment,
  validateCreateRequest,
  validateUpdateRequest,
} from './schema/appointment-schema';

// Event builders
export {
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
  createAppointmentConfirmedEvent,
  createAppointmentCancelledEvent,
  createAppointmentCompletedEvent,
  createAppointmentRescheduledEvent,
  createAppointmentNoShowEvent,
  createAppointmentStartedEvent,
} from './eventing/appointment-events';

// Schema generators
export { generatePrismaSchema } from './schema/appointment-schema';
