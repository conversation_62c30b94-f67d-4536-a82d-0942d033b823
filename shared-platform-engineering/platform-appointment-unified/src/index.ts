/**
 * 🎯 UNIFIED APPOINTMENT LIBRARY - Main Entry Point
 *
 * This library consolidates all appointment-related functionality:
 * - Schema definitions and validation
 * - Domain entities and business logic
 * - Event definitions and publishers
 * - Repository interfaces and adapters
 * - Application service interfaces
 */

// ============================================================================
// 🔥 SCHEMA LAYER - Single Source of Truth
// ============================================================================
export * from './schema';

// ============================================================================
// 🔥 DOMAIN LAYER - Business Logic
// ============================================================================
export { AppointmentEntity } from './domain/AppointmentEntity';
export type { IAppointmentProps } from './domain/AppointmentEntity';

// ============================================================================
// 🔥 VALIDATION LAYER
// ============================================================================
export { AppointmentModel } from './validation/AppointmentModel';

// ============================================================================
// 🔧 UTILITY EXPORTS
// ============================================================================
export * from './types/unified-types';

// ============================================================================
// 🗄️ REPOSITORY EXPORTS
// ============================================================================
export { SimpleAppointmentRepository } from './repositories/SimpleAppointmentRepository';

// Note: Eventing, Infrastructure, and complex Repository exports are temporarily
// commented out until dependencies are resolved
// export * from './eventing';
// export * from './infrastructure';
// export * from './repositories/AppointmentRepository';

// ============================================================================
// 🎯 CONVENIENCE RE-EXPORTS
// ============================================================================

// Most commonly used types
export type {
  Appointment,
  AppointmentStatus,
  AppointmentSource,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  AppointmentResponse,
  AppointmentQuery,
  UnifiedAppointment, // Legacy alias
} from './schema/appointment-schema';

// Note: Event and infrastructure type exports are temporarily commented out
// until dependencies are resolved

// Validation functions
export {
  validateAppointment,
  validateCreateRequest,
  validateUpdateRequest,
} from './schema/appointment-schema';

// Note: Event builders temporarily commented out until dependencies are resolved
// export {
//   createAppointmentCreatedEvent,
//   createAppointmentUpdatedEvent,
//   createAppointmentConfirmedEvent,
//   createAppointmentCancelledEvent,
//   createAppointmentCompletedEvent,
//   createAppointmentRescheduledEvent,
//   createAppointmentNoShowEvent,
//   createAppointmentStartedEvent,
// } from './eventing/appointment-events';

// Schema generators
export { generatePrismaSchema } from './schema/appointment-schema';
