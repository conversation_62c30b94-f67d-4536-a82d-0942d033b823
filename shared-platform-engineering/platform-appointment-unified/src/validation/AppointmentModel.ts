/**
 * 🎯 APPOINTMENT MODEL - Class Validator Enhanced
 *
 * Uses shared schema library as single source of truth
 * Enhanced with class-validator decorators for runtime validation
 */

import 'reflect-metadata';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsDate,
  IsEnum,
  IsN<PERSON>ber,
  IsPositive,
  IsInt,
  MinLength,
  IsUrl,
  validateSync,
  type ValidationError,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  type Appointment as SharedAppointment,
  type AppointmentStatus as SharedAppointmentStatus,
  type CreateAppointmentRequest,
  validateAppointment,
  validateCreateRequest,
} from '../schema/appointment-schema';

// ============================================================================
// 🔥 ENUMS FOR CLASS VALIDATION
// ============================================================================

export enum AppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  RESCHEDULED = 'RESCHEDULED',
}

export enum AppointmentSource {
  PLANNER = 'planner',
  MANAGEMENT = 'management',
  API = 'api',
  IMPORT = 'import',
}

// ============================================================================
// 🔥 DOMAIN MODEL - Enhanced with Validation Decorators
// ============================================================================

export class AppointmentModel implements SharedAppointment {
  @IsString()
  id!: string;

  @IsString()
  salonId!: string;

  @IsString()
  customerId!: string;

  @IsOptional()
  @IsString()
  staffId?: string;

  @IsString()
  treatmentId!: string;

  // Customer Info (denormalized)
  @IsString()
  @MinLength(1)
  customerName!: string;

  @IsEmail()
  customerEmail!: string;

  @IsOptional()
  @IsString()
  customerPhone?: string;

  // Treatment Info (denormalized)
  @IsString()
  @MinLength(1)
  treatmentName!: string;

  @IsInt()
  @IsPositive()
  treatmentDuration!: number;

  @IsNumber()
  @IsPositive()
  treatmentPrice!: number;

  // Salon Info (denormalized)
  @IsString()
  @MinLength(1)
  salonName!: string;

  @IsOptional()
  @IsUrl()
  salonLogo?: string;

  @IsOptional()
  @IsString()
  salonColor?: string;

  // Scheduling
  @IsDate()
  @Type(() => Date)
  startTime!: Date;

  @IsDate()
  @Type(() => Date)
  endTime!: Date;

  // Status & Metadata
  @IsEnum(AppointmentStatus)
  status!: AppointmentStatus;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsString()
  @Transform(({ value }) => value || 'en-US')
  locale!: string;

  // Source tracking
  @IsEnum(AppointmentSource)
  @Transform(({ value }) => value || AppointmentSource.PLANNER)
  source!: AppointmentSource;

  @IsOptional()
  @IsString()
  plannerAppointmentId?: string;

  // Timestamps
  @IsDate()
  @Type(() => Date)
  createdAt!: Date;

  @IsDate()
  @Type(() => Date)
  updatedAt!: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  confirmedAt?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  completedAt?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  cancelledAt?: Date;

  constructor(data?: Partial<AppointmentModel>) {
    if (data) {
      Object.assign(this, data);
    }
  }

  // ============================================================================
  // 🎯 VALIDATION METHODS
  // ============================================================================

  /**
   * Validates the appointment using class-validator
   */
  validate(): ValidationError[] {
    return validateSync(this);
  }

  /**
   * Checks if the appointment is valid
   */
  isValid(): boolean {
    return this.validate().length === 0;
  }

  /**
   * Validates and throws if invalid
   */
  validateOrThrow(): void {
    const errors = this.validate();
    if (errors.length > 0) {
      throw new Error(
        `Validation failed: ${errors.map((e) => e.toString()).join(', ')}`,
      );
    }
  }

  // ============================================================================
  // 🎯 BUSINESS LOGIC METHODS
  // ============================================================================

  /**
   * Confirms the appointment
   */
  confirm(): void {
    if (this.status === AppointmentStatus.CANCELLED) {
      throw new Error('Cannot confirm a cancelled appointment');
    }
    this.status = AppointmentStatus.CONFIRMED;
    this.confirmedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Cancels the appointment
   */
  cancel(reason?: string): void {
    if (this.status === AppointmentStatus.COMPLETED) {
      throw new Error('Cannot cancel a completed appointment');
    }
    this.status = AppointmentStatus.CANCELLED;
    this.cancelledAt = new Date();
    this.updatedAt = new Date();
    if (reason) {
      this.notes = this.notes
        ? `${this.notes}\nCancellation reason: ${reason}`
        : `Cancellation reason: ${reason}`;
    }
  }

  /**
   * Completes the appointment
   */
  complete(): void {
    if (
      this.status !== AppointmentStatus.IN_PROGRESS &&
      this.status !== AppointmentStatus.CONFIRMED
    ) {
      throw new Error(
        'Can only complete appointments that are in progress or confirmed',
      );
    }
    this.status = AppointmentStatus.COMPLETED;
    this.completedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Reschedules the appointment
   */
  reschedule(newStartTime: Date, newEndTime: Date): void {
    this.startTime = newStartTime;
    this.endTime = newEndTime;
    this.status = AppointmentStatus.RESCHEDULED;
    this.updatedAt = new Date();
  }

  /**
   * Marks appointment as no-show
   */
  markNoShow(): void {
    this.status = AppointmentStatus.NO_SHOW;
    this.updatedAt = new Date();
  }

  /**
   * Starts the appointment (sets to in progress)
   */
  start(): void {
    if (this.status !== AppointmentStatus.CONFIRMED) {
      throw new Error('Can only start confirmed appointments');
    }
    this.status = AppointmentStatus.IN_PROGRESS;
    this.updatedAt = new Date();
  }

  // ============================================================================
  // 🎯 UTILITY METHODS
  // ============================================================================

  /**
   * Checks if appointment is in the past
   */
  isPast(): boolean {
    return this.endTime < new Date();
  }

  /**
   * Checks if appointment is upcoming
   */
  isUpcoming(): boolean {
    return this.startTime > new Date();
  }

  /**
   * Checks if appointment is currently active
   */
  isActive(): boolean {
    const now = new Date();
    return this.startTime <= now && this.endTime >= now;
  }

  /**
   * Gets appointment duration in minutes
   */
  getDurationMinutes(): number {
    return Math.round(
      (this.endTime.getTime() - this.startTime.getTime()) / (1000 * 60),
    );
  }

  /**
   * Checks if appointment can be cancelled
   */
  canBeCancelled(): boolean {
    return (
      this.status !== AppointmentStatus.COMPLETED &&
      this.status !== AppointmentStatus.CANCELLED &&
      this.status !== AppointmentStatus.NO_SHOW
    );
  }

  /**
   * Checks if appointment can be rescheduled
   */
  canBeRescheduled(): boolean {
    return (
      this.status === AppointmentStatus.PENDING ||
      this.status === AppointmentStatus.CONFIRMED ||
      this.status === AppointmentStatus.SCHEDULED
    );
  }

  /**
   * Converts to plain object (compatible with shared schema)
   */
  toPlainObject(): SharedAppointment {
    return {
      id: this.id,
      salonId: this.salonId,
      customerId: this.customerId,
      staffId: this.staffId,
      treatmentId: this.treatmentId,
      customerName: this.customerName,
      customerEmail: this.customerEmail,
      customerPhone: this.customerPhone,
      treatmentName: this.treatmentName,
      treatmentDuration: this.treatmentDuration,
      treatmentPrice: this.treatmentPrice,
      salonName: this.salonName,
      salonLogo: this.salonLogo,
      salonColor: this.salonColor,
      startTime: this.startTime,
      endTime: this.endTime,
      status: this.status as SharedAppointmentStatus,
      notes: this.notes,
      locale: this.locale,
      source: this.source,
      plannerAppointmentId: this.plannerAppointmentId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      confirmedAt: this.confirmedAt,
      completedAt: this.completedAt,
      cancelledAt: this.cancelledAt,
    };
  }

  /**
   * Creates a copy of the appointment
   */
  clone(): AppointmentModel {
    return new AppointmentModel(this.toPlainObject() as any); // Temporary cast to fix type issue
  }
}

// ============================================================================
// 🎯 FACTORY FUNCTIONS
// ============================================================================

/**
 * Creates an AppointmentModel from shared schema data
 */
export function createAppointmentFromShared(
  data: SharedAppointment,
): AppointmentModel {
  const appointment = new AppointmentModel();
  Object.assign(appointment, data);
  return appointment;
}

/**
 * Creates an AppointmentModel from create request
 */
export function createAppointmentFromRequest(
  data: CreateAppointmentRequest,
): AppointmentModel {
  // Validate using shared schema first
  const validatedData = validateCreateRequest(data);

  const appointment = new AppointmentModel();
  Object.assign(appointment, {
    ...validatedData,
    id: '', // Will be set by repository
    createdAt: new Date(),
    updatedAt: new Date(),
    status: AppointmentStatus.PENDING,
    locale: validatedData.locale || 'en-US',
    source:
      (validatedData.source as AppointmentSource) || AppointmentSource.PLANNER,
  });

  return appointment;
}

/**
 * Validates an appointment using class-validator
 */
export function validateAppointmentClass(
  appointment: AppointmentModel,
): ValidationError[] {
  return validateSync(appointment);
}

/**
 * Checks if an appointment class is valid
 */
export function isAppointmentClassValid(
  appointment: AppointmentModel,
): boolean {
  return validateAppointmentClass(appointment).length === 0;
}

// ============================================================================
// 🎯 TYPE EXPORTS
// ============================================================================

export type AppointmentProps = CreateAppointmentRequest;

// Re-export shared types for convenience
export type {
  Appointment as SharedAppointment,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  AppointmentResponse,
  AppointmentStatus as SharedAppointmentStatus,
} from '../schema/appointment-schema';
