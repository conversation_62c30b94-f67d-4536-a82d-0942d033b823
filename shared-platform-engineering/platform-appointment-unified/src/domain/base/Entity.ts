/**
 * Simple Entity base class implementation
 * This replaces the external dependency on @beauty-crm/product-domain-types
 */

export class UniqueEntityID {
  private value: string;

  constructor(id?: string) {
    this.value = id || crypto.randomUUID();
  }

  equals(id?: UniqueEntityID): boolean {
    if (id === null || id === undefined) {
      return false;
    }
    return id.toValue() === this.value;
  }

  toString(): string {
    return String(this.value);
  }

  toValue(): string {
    return this.value;
  }
}

export abstract class Entity<T> {
  protected readonly _id: UniqueEntityID;
  protected props: T;

  constructor(props: T, id?: UniqueEntityID) {
    this._id = id || new UniqueEntityID();
    this.props = props;
  }

  public equals(object?: Entity<T>): boolean {
    if (object === null || object === undefined) {
      return false;
    }

    if (this === object) {
      return true;
    }

    if (!(object instanceof Entity)) {
      return false;
    }

    return this._id.equals(object._id);
  }

  get id(): UniqueEntityID {
    return this._id;
  }

  get idValue(): string {
    return this._id.toValue();
  }
}

export interface IBaseEntityProps {
  createdAt: Date;
  updatedAt: Date;
}
