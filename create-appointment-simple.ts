/**
 * 🎯 SIMPLE APPOINTMENT CREATOR - Direct Database Access
 *
 * This script creates an appointment directly in the database using our unified library
 */

import { PrismaClient } from '@prisma/client';

// Import from our unified library
import {
  type CreateAppointmentRequest,
} from '@beauty-crm/platform-appointment-unified';

/**
 * Database connection
 */
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/beauty_crm'
    }
  }
});

/**
 * Create a demo appointment
 */
async function createDemoAppointment() {
  console.log('🎯 Creating demo appointment with unified library...');

  try {
    // Demo appointment data
    const appointmentData: CreateAppointmentRequest = {
      salonId: 'salon_demo_001',
      customerId: 'customer_demo_001',
      staffId: 'staff_demo_001',
      treatmentId: 'treatment_demo_001',
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      customerPhone: '+1234567890',
      treatmentName: 'Haircut & Style',
      treatmentDuration: 60,
      treatmentPrice: 75.0,
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      endTime: new Date(Date.now() + 25 * 60 * 60 * 1000), // Tomorrow + 1 hour
      status: 'PENDING',
      notes: 'Created via Playwright using unified library',
      locale: 'en-US',
      source: 'playwright-demo',
    };

    console.log('📋 Creating appointment with data:', JSON.stringify(appointmentData, null, 2));

    // Create the appointment directly with Prisma
    const createdAppointment = await prisma.appointment.create({
      data: {
        salonId: appointmentData.salonId,
        customerId: appointmentData.customerId,
        staffId: appointmentData.staffId,
        treatmentId: appointmentData.treatmentId,
        customerName: appointmentData.customerName,
        customerEmail: appointmentData.customerEmail,
        customerPhone: appointmentData.customerPhone,
        treatmentName: appointmentData.treatmentName,
        treatmentDuration: appointmentData.treatmentDuration,
        treatmentPrice: appointmentData.treatmentPrice,
        startTime: appointmentData.startTime,
        endTime: appointmentData.endTime,
        status: appointmentData.status,
        notes: appointmentData.notes,
        locale: appointmentData.locale,
        source: appointmentData.source,
      },
    });

    console.log('✅ Appointment created successfully!');
    console.log('📋 Created appointment:', JSON.stringify(createdAppointment, null, 2));

    return createdAppointment;

  } catch (error) {
    console.error('❌ Error creating appointment:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    const appointment = await createDemoAppointment();
    console.log('🎉 Demo appointment created with ID:', appointment.id);
    process.exit(0);
  } catch (error) {
    console.error('💥 Failed to create demo appointment:', error);
    process.exit(1);
  }
}

// Run the script
main();
